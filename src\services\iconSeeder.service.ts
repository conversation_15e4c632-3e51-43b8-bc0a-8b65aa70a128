import fs from "fs";
import path from "path";
import {
  getHash,
  RECIPE_FILE_UPLOAD_CONSTANT,
  getMimeTypeFromExtension,
} from "../helper/common";
import { db } from "../models/index";
import {
  item_type,
  item_status,
  item_IEC,
  item_external_location,
} from "../models/Item";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";

// Setup MinIO client
const s3 = new S3Client({
  endpoint: global.config.MINIO_ENDPOINT,
  region: "us-east-1",
  forcePathStyle: true,
  credentials: {
    accessKeyId: global.config.MINIO_ACCESS_KEY,
    secretAccessKey: global.config.MINIO_SECRET_KEY,
  },
});

/**
 * Universal Icon Seeder Service
 * Handles icon uploading for categories, attributes, and other entities during seeding
 */
class IconSeederService {
  private readonly bucketName: string;

  constructor() {
    this.bucketName =
      global.config.MINIO_BUCKET_NAME || process.env.NODE_ENV || "development";
  }

  /**
   * Upload icon for recipe categories
   */
  async uploadRecipeCategoryIcon(
    categoryName: string,
    iconFileName: string,
    iconsBasePath: string = "src/icons/recipe_category"
  ): Promise<number | null> {
    const iconPath = path.join(iconsBasePath, iconFileName);
    const storageFileName = `${categoryName.toLowerCase().replace(/\s+/g, "-")}-icon.png`;
    const s3FilePath =
      RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(
        null, // organization name (null for system defaults)
        null, // category ID (not needed for file path)
        storageFileName
      );

    return this.uploadIconToS3(
      categoryName,
      iconPath,
      storageFileName,
      s3FilePath,
      "recipe_category_icon"
    );
  }

  /**
   * Upload icon for ingredient categories
   */
  async uploadIngredientCategoryIcon(
    categoryName: string,
    iconFileName: string,
    iconsBasePath: string = "src/icons/ingredient_category"
  ): Promise<number | null> {
    const iconPath = path.join(iconsBasePath, iconFileName);
    const storageFileName = `${categoryName.toLowerCase().replace(/\s+/g, "-")}-icon.png`;
    const s3FilePath =
      RECIPE_FILE_UPLOAD_CONSTANT.INGREDIENT_CATEGORY_ICON.destinationPath(
        null, // organization name (null for system defaults)
        null, // category ID (not needed for file path)
        storageFileName
      );

    return this.uploadIconToS3(
      categoryName,
      iconPath,
      storageFileName,
      s3FilePath,
      "ingredient_category_icon"
    );
  }

  /**
   * Upload icon for food attributes (allergens, dietary, etc.)
   */
  async uploadAttributeIcon(
    attributeName: string,
    iconFileName: string,
    iconsBasePath: string = "src/icons/Allergens"
  ): Promise<number | null> {
    const iconPath = path.join(iconsBasePath, iconFileName);
    const storageFileName = `${attributeName.toLowerCase().replace(/\s+/g, "-")}-icon.png`;
    const s3FilePath =
      RECIPE_FILE_UPLOAD_CONSTANT.ATTRIBUTE_ICON.destinationPath(
        null, // organization name (null for system defaults)
        null, // attribute ID (not needed for file path)
        storageFileName
      );

    return this.uploadIconToS3(
      attributeName,
      iconPath,
      storageFileName,
      s3FilePath,
      "attribute_icon"
    );
  }

  /**
   * Generic method to upload icon to S3 and create Item record
   */
  private async uploadIconToS3(
    entityName: string,
    iconPath: string,
    storageFileName: string,
    s3FilePath: string,
    itemCategory: string
  ): Promise<number | null> {
    try {
      // Check if file exists
      if (!fs.existsSync(iconPath)) {
        console.warn(`⚠️  Icon file not found for ${entityName}: ${iconPath}`);
        return null;
      }

      // Read file
      const fileBuffer = fs.readFileSync(iconPath);
      const fileExtension = path.extname(storageFileName);

      // Generate hash for duplicate detection
      const fileHashResult = await getHash(fileBuffer);
      if (!fileHashResult.status) {
        console.error(
          `Failed to generate hash for ${entityName} icon:`,
          fileHashResult.message
        );
        return null;
      }

      // Check if item already exists with same hash or same name pattern
      const existingItem = await db.Item.findOne({
        where: {
          item_hash: fileHashResult.hash,
          item_organization_id: null,
        },
      });

      if (existingItem) {
        console.log(
          `✅ Icon already exists for ${entityName}, reusing existing item ID: ${existingItem.id}`
        );
        return existingItem.id;
      }

      // Also check if an item with similar name already exists (to avoid duplicates with different hashes)
      const existingItemByName = await db.Item.findOne({
        where: {
          item_name: storageFileName,
          item_organization_id: null,
          item_category: itemCategory,
        },
      });

      if (existingItemByName) {
        console.log(
          `✅ Icon with similar name already exists for ${entityName}, reusing existing item ID: ${existingItemByName.id}`
        );
        return existingItemByName.id;
      }

      // Upload to S3
      await s3.send(
        new PutObjectCommand({
          Bucket: this.bucketName,
          Key: s3FilePath,
          Body: fileBuffer,
          ContentType:
            fileHashResult.actualMimeType ||
            getMimeTypeFromExtension(fileExtension),
        })
      );

      console.log(`📤 Uploaded ${entityName} icon to S3: ${s3FilePath}`);

      // Create Item record
      const itemData = {
        item_type: item_type.IMAGE,
        item_name: storageFileName,
        item_hash: fileHashResult.hash,
        item_mime_type:
          fileHashResult.actualMimeType ||
          getMimeTypeFromExtension(fileExtension),
        item_extension: fileExtension,
        item_size: fileBuffer.length,
        item_IEC: item_IEC.B,
        item_status: item_status.ACTIVE,
        item_external_location: item_external_location.NO,
        item_location: s3FilePath,
        item_organization_id: null,
        item_category: itemCategory,
        created_by: 1,
        updated_by: 1,
      };

      const item = await db.Item.create(itemData);
      console.log(
        `✅ Created item record for ${entityName} icon with ID: ${item.id}`
      );

      return item.id;
    } catch (error) {
      console.error(`❌ Error uploading icon for ${entityName}:`, error);
      return null;
    }
  }

  /**
   * Get default icon mapping for recipe categories
   */
  getRecipeCategoryIconMapping(): Record<string, string> {
    return {
      "Main Course": "Main Courses Stroke Black.png",
      Appetizer: "Appetizers & Starters Stroke Black.png",
      Dessert: "Dessert cake Stroke Black.png",
      Beverage: "Beverages & Drinks Stroke Black.png",
      Soup: "Soups Stroke Black.png",
      Salad: "salad Stroke Black.png",
      Snack: "snacks Stroke Black.png",
      Breakfast: "breakfast Stroke Black.png",
      "Baked Goods": "Baked Goods Stroke Black.png",
      "Sauces & Dressings": "sauce& Dressings Stroke Black.png",
      "Side Dishes": "Side Dishes Stroke Black.png",
    };
  }

  /**
   * Get default icon mapping for ingredient categories
   */
  getIngredientCategoryIconMapping(): Record<string, string> {
    return {
      Dairy: "milk Stroke Black.png",
      Meat: "meat Stroke Black.png",
      Poultry: "Poultry Stroke Black.png",
      Seafood: "Sea Food Stroke Black.png",
      Vegetables: "vegetables Stroke Black.png",
      Fruits: "fruits Stroke Black.png",
      Grains: "Grains Stroke Black.png",
      Nuts: "Nuts & Seeds Stroke Black.png",
      "Herbs & Spices": "Herbs & Spices Stroke Black.png",
      Oils: "oil Stroke Black.png",
      Condiments: "sauce Stroke Black.png",
      Baking: "baking & sweeteners  Stroke Black.png", // Note: extra space in filename
      "Dry Goods": "Dry Goods & Pulses Stroke Black.png",
      Beverages: "Beverages & Drinks Stroke Black.png",
    };
  }

  /**
   * Get default icon mapping for allergens
   */
  getAllergenIconMapping(): Record<string, string> {
    return {
      Gluten: "gluten Stroke.png",
      Crustaceans: "Crustaceans Stroke.png",
      Eggs: "Egg Stroke.png",
      Fish: "Fish Stroke.png",
      Milk: "Milk Stroke Blue.png",
      Molluscs: "Molluscs Stroke.png",
      Peanuts: "Peanuts Stroke.png",
      "Tree Nuts": "Tree Nut Stroke.png",
      Soy: "soybean Stroke.png",
      Sesame: "Sesame Stroke.png",
      Celery: "celery Stroke.png",
      Mustard: "Mustard Stroke.png",
      Sulphites: "Sulphite Stroke.png",
      Lupin: "lupin Stroke.png",
    };
  }
}

export default new IconSeederService();
