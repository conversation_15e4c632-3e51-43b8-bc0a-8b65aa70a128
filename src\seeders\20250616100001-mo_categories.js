const { QueryTypes } = require("sequelize");
const iconSeederService = require("../services/iconSeeder.service").default;

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if categories already exist
    const existingCategories = await queryInterface.sequelize.query(
      `SELECT * FROM mo_category WHERE organization_id IS NULL AND is_system_category = true`,
      { type: QueryTypes.SELECT }
    );

    // Recipe Categories
    const recipeCategories = [
      { name: "Main Course", slug: "main-course", type: "recipe" },
      { name: "Appetizer", slug: "appetizer", type: "recipe" },
      { name: "Dessert", slug: "dessert", type: "recipe" },
      { name: "Beverage", slug: "beverage", type: "recipe" },
      { name: "Soup", slug: "soup", type: "recipe" },
      { name: "Salad", slug: "salad", type: "recipe" },
      { name: "Snack", slug: "snack", type: "recipe" },
      { name: "Breakfast", slug: "breakfast", type: "recipe" },
      { name: "Baked Goods", slug: "baked-goods", type: "recipe" },
      {
        name: "Sauces & Dressings",
        slug: "sauces-dressings",
        type: "recipe",
      },
      { name: "Side Dishes", slug: "side-dishes", type: "recipe" },
    ];

    // Ingredient Categories
    const ingredientCategories = [
      { name: "Dairy", slug: "dairy", type: "ingredient" },
      { name: "Meat", slug: "meat", type: "ingredient" },
      { name: "Poultry", slug: "poultry", type: "ingredient" },
      { name: "Seafood", slug: "seafood", type: "ingredient" },
      { name: "Vegetables", slug: "vegetables", type: "ingredient" },
      { name: "Fruits", slug: "fruits", type: "ingredient" },
      { name: "Grains", slug: "grains", type: "ingredient" },
      { name: "Nuts", slug: "nuts", type: "ingredient" },
      { name: "Herbs & Spices", slug: "herbs-spices", type: "ingredient" },
      { name: "Oils", slug: "oils", type: "ingredient" },
      { name: "Condiments", slug: "condiments", type: "ingredient" },
      { name: "Baking", slug: "baking", type: "ingredient" },
      { name: "Dry Goods", slug: "dry-goods", type: "ingredient" },
      { name: "Beverages", slug: "beverages", type: "ingredient" },
    ];

    if (existingCategories.length === 0) {
      // Combine all categories
      const allCategories = [...recipeCategories, ...ingredientCategories];

      // Prepare bulk insert data
      const categoryData = allCategories.map((category) => ({
        category_name: category.name,
        category_slug: category.slug,
        category_type: category.type,
        category_icon: null,
        category_status: "active",
        organization_id: null,
        is_system_category: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("mo_category", categoryData);
      console.log("✅ Categories seeded successfully");
    } else {
      console.log(
        "⏭️  Categories already exist, skipping category creation..."
      );
    }

    // Upload icons for categories (regardless of whether categories were just created or already existed)
    console.log("📤 Starting icon upload for categories...");

    // Get icon mappings
    const recipeIconMapping = iconSeederService.getRecipeCategoryIconMapping();
    const ingredientIconMapping =
      iconSeederService.getIngredientCategoryIconMapping();

    // Upload recipe category icons
    for (const category of recipeCategories) {
      const iconFileName = recipeIconMapping[category.name];
      if (iconFileName) {
        // Check if category already has an icon
        const existingCategory = await queryInterface.sequelize.query(
          `SELECT category_icon FROM mo_category WHERE category_name = :categoryName AND category_type = 'recipe' AND organization_id IS NULL`,
          {
            replacements: { categoryName: category.name },
            type: QueryTypes.SELECT,
          }
        );

        if (existingCategory.length > 0 && existingCategory[0].category_icon) {
          console.log(
            `✅ ${category.name} already has an icon, skipping upload`
          );
          continue;
        }

        console.log(`📤 Uploading icon for recipe category: ${category.name}`);
        const iconId = await iconSeederService.uploadRecipeCategoryIcon(
          category.name,
          iconFileName
        );

        if (iconId) {
          // Update category with icon ID
          await queryInterface.sequelize.query(
            `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'recipe' AND organization_id IS NULL`,
            {
              replacements: { iconId, categoryName: category.name },
              type: QueryTypes.UPDATE,
            }
          );
          console.log(`✅ Updated ${category.name} with icon ID: ${iconId}`);
        }
      } else {
        console.log(
          `⚠️  No icon mapping found for recipe category: ${category.name}`
        );
      }
    }

    // Upload ingredient category icons
    for (const category of ingredientCategories) {
      const iconFileName = ingredientIconMapping[category.name];
      if (iconFileName) {
        // Check if category already has an icon
        const existingCategory = await queryInterface.sequelize.query(
          `SELECT category_icon FROM mo_category WHERE category_name = :categoryName AND category_type = 'ingredient' AND organization_id IS NULL`,
          {
            replacements: { categoryName: category.name },
            type: QueryTypes.SELECT,
          }
        );

        if (existingCategory.length > 0 && existingCategory[0].category_icon) {
          console.log(
            `✅ ${category.name} already has an icon, skipping upload`
          );
          continue;
        }

        console.log(
          `📤 Uploading icon for ingredient category: ${category.name}`
        );
        const iconId = await iconSeederService.uploadIngredientCategoryIcon(
          category.name,
          iconFileName
        );

        if (iconId) {
          // Update category with icon ID
          await queryInterface.sequelize.query(
            `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'ingredient' AND organization_id IS NULL`,
            {
              replacements: { iconId, categoryName: category.name },
              type: QueryTypes.UPDATE,
            }
          );
          console.log(`✅ Updated ${category.name} with icon ID: ${iconId}`);
        }
      } else {
        console.log(
          `⚠️  No icon mapping found for ingredient category: ${category.name}`
        );
      }
    }

    console.log("✅ Category icons upload completed");
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_category", {
      organization_id: null,
      is_system_category: true,
    });
  },
};
