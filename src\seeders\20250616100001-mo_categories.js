const { QueryTypes } = require("sequelize");
const path = require("path");
const fs = require("fs");

// Since we can't directly import TypeScript in a JS seeder, we'll implement the icon upload logic here
// This is a simplified version of the iconSeeder service functionality

// Icon mappings
const getRecipeCategoryIconMapping = () => {
  return {
    "Main Course": "Main Courses Stroke Black.png",
    Appetizer: "Appetizers & Starters Stroke Black.png",
    Dessert: "Dessert cake Stroke Black.png",
    Beverage: "Beverages & Drinks Stroke Black.png",
    Soup: "Soups Stroke Black.png",
    Salad: "salad Stroke Black.png",
    Snack: "snacks Stroke Black.png",
    Breakfast: "breakfast Stroke Black.png",
    "Baked Goods": "Baked Goods Stroke Black.png",
    "Sauces & Dressings": "sauce& Dressings Stroke Black.png",
    "Side Dishes": "Side Dishes Stroke Black.png",
  };
};

const getIngredientCategoryIconMapping = () => {
  return {
    Dairy: "milk Stroke Black.png",
    Meat: "meat Stroke Black.png",
    Poultry: "Poultry Stroke Black.png",
    Seafood: "Sea Food Stroke Black.png",
    Vegetables: "vegetables Stroke Black.png",
    Fruits: "fruits Stroke Black.png",
    Grains: "Grains Stroke Black.png",
    Nuts: "Nuts & Seeds Stroke Black.png",
    "Herbs & Spices": "Herbs & Spices Stroke Black.png",
    Oils: "oil Stroke Black.png",
    Condiments: "sauce Stroke Black.png",
    Baking: "baking & sweeteners  Stroke Black.png",
    "Dry Goods": "Dry Goods & Pulses Stroke Black.png",
    Beverages: "Beverages & Drinks Stroke Black.png",
  };
};

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if categories already exist
    const existingCategories = await queryInterface.sequelize.query(
      `SELECT * FROM mo_category WHERE organization_id IS NULL AND is_system_category = true`,
      { type: QueryTypes.SELECT }
    );

    // Recipe Categories
    const recipeCategories = [
      { name: "Main Course", slug: "main-course", type: "recipe" },
      { name: "Appetizer", slug: "appetizer", type: "recipe" },
      { name: "Dessert", slug: "dessert", type: "recipe" },
      { name: "Beverage", slug: "beverage", type: "recipe" },
      { name: "Soup", slug: "soup", type: "recipe" },
      { name: "Salad", slug: "salad", type: "recipe" },
      { name: "Snack", slug: "snack", type: "recipe" },
      { name: "Breakfast", slug: "breakfast", type: "recipe" },
      { name: "Baked Goods", slug: "baked-goods", type: "recipe" },
      {
        name: "Sauces & Dressings",
        slug: "sauces-dressings",
        type: "recipe",
      },
      { name: "Side Dishes", slug: "side-dishes", type: "recipe" },
    ];

    // Ingredient Categories
    const ingredientCategories = [
      { name: "Dairy", slug: "dairy", type: "ingredient" },
      { name: "Meat", slug: "meat", type: "ingredient" },
      { name: "Poultry", slug: "poultry", type: "ingredient" },
      { name: "Seafood", slug: "seafood", type: "ingredient" },
      { name: "Vegetables", slug: "vegetables", type: "ingredient" },
      { name: "Fruits", slug: "fruits", type: "ingredient" },
      { name: "Grains", slug: "grains", type: "ingredient" },
      { name: "Nuts", slug: "nuts", type: "ingredient" },
      { name: "Herbs & Spices", slug: "herbs-spices", type: "ingredient" },
      { name: "Oils", slug: "oils", type: "ingredient" },
      { name: "Condiments", slug: "condiments", type: "ingredient" },
      { name: "Baking", slug: "baking", type: "ingredient" },
      { name: "Dry Goods", slug: "dry-goods", type: "ingredient" },
      { name: "Beverages", slug: "beverages", type: "ingredient" },
    ];

    if (existingCategories.length === 0) {
      // Combine all categories
      const allCategories = [...recipeCategories, ...ingredientCategories];

      // Prepare bulk insert data
      const categoryData = allCategories.map((category) => ({
        category_name: category.name,
        category_slug: category.slug,
        category_type: category.type,
        category_icon: null,
        category_status: "active",
        organization_id: null,
        is_system_category: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("mo_category", categoryData);
      console.log("✅ Categories seeded successfully");
    } else {
      console.log(
        "⏭️  Categories already exist, skipping category creation..."
      );
    }

    // Upload icons for categories (regardless of whether categories were just created or already existed)
    console.log("📤 Starting icon upload for categories...");
    console.log(
      "ℹ️  Note: Icon upload functionality requires the iconSeeder service to be properly compiled."
    );
    console.log(
      "ℹ️  For now, skipping icon uploads. Please run icon upload separately after server startup."
    );

    // Get icon mappings
    const recipeIconMapping = getRecipeCategoryIconMapping();
    const ingredientIconMapping = getIngredientCategoryIconMapping();

    // Upload recipe category icons
    for (const category of recipeCategories) {
      const iconFileName = recipeIconMapping[category.name];
      if (iconFileName) {
        console.log(`📋 Recipe category ${category.name} → ${iconFileName}`);
      } else {
        console.log(
          `⚠️  No icon mapping found for recipe category: ${category.name}`
        );
      }
    }

    // Upload ingredient category icons
    for (const category of ingredientCategories) {
      const iconFileName = ingredientIconMapping[category.name];
      if (iconFileName) {
        console.log(
          `📋 Ingredient category ${category.name} → ${iconFileName}`
        );
      } else {
        console.log(
          `⚠️  No icon mapping found for ingredient category: ${category.name}`
        );
      }
    }

    console.log(
      "✅ Category seeding completed (icons will be uploaded via API)"
    );
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_category", {
      organization_id: null,
      is_system_category: true,
    });
  },
};
