import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeStepsStatus } from "../models/RecipeSteps";
import { RecipeResourceStatus } from "../models/RecipeResources";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { generateUniqueSlug } from "../helper/slugGenerator";
import { RecipeIngredientsNutritionCuisineData } from "../types/recipe-batch.types";

// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;
const RecipeSteps = db.RecipeSteps;
const RecipeResources = db.RecipeResources;
const RecipeHistory = db.RecipeHistory;

import {
  RECIPE_FILE_UPLOAD_CONSTANT
} from "../helper/common";
import { createRecipeHistory } from "../helper/recipe.helper";
import {
  updateRecipeCostTimestamp,
  updateRecipeNutritionTimestamp,
} from "../helper/timestamp.helper";
import {
  TransactionManager,
  FileOperationTracker,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import uploadService from "../helper/upload.service";

// ============================================================================
// CONSTANTS AND SHARED UTILITIES
// ============================================================================

// Constants for batch processing (kept for backward compatibility)
const STEPS_BATCH_SIZE = 5;
const UPLOADS_BATCH_SIZE = 5;

// Helper function to check if user can create/update recipes
const canCreateUpdateRecipes = (userRole: any): boolean => {
  if (!userRole) return false;

  const ADMIN_SIDE_USER = ["admin", "chef", "kitchen_manager", "recipe_manager"];
  return ADMIN_SIDE_USER.includes(userRole.role_name);
};

// Common authorization check for batch operations
const checkBatchAuthorization = (req: Request, res: Response) => {
  const userId = (req as any).user?.id;
  const organizationId = (req as any).user?.organization_id;
  const userRole = (req as any).user?.roles?.[0];

  if (!userId) {
    return {
      error: res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: "Unauthorized access",
      }),
      userId: null,
      organizationId: null,
      userRole: null
    };
  }

  if (!canCreateUpdateRecipes(userRole)) {
    return {
      error: res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      }),
      userId: null,
      organizationId: null,
      userRole: null
    };
  }

  return {
    error: null,
    userId,
    organizationId,
    userRole
  };
};

// Common recipe validation
const validateRecipeAccess = async (recipeId: number, organizationId: string, transaction: any) => {
  return await Recipe.findOne({
    where: {
      id: recipeId,
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    },
    transaction,
  });
};

// Helper function to associate uploaded files with a recipe
const associateFilesWithRecipe = async (
  recipeId: number,
  fileIds: number[],
  organizationId: string,
  userId: number,
  transaction: any
): Promise<{ success: boolean; updatedFiles: number; message: string }> => {
  if (!fileIds || fileIds.length === 0) {
    return {
      success: true,
      updatedFiles: 0,
      message: "No files to associate"
    };
  }

  // Find temporary files that belong to the user's organization
  const tempFiles = await RecipeResources.findAll({
    where: {
      id: {
        [Op.in]: fileIds
      },
      organization_id: organizationId,
      status: "temporary",
      recipe_id: null
    },
    transaction,
  });

  if (tempFiles.length === 0) {
    return {
      success: false,
      updatedFiles: 0,
      message: "No valid temporary files found to associate"
    };
  }

  // Update the files to associate them with the recipe
  const updatedCount = await RecipeResources.update(
    {
      recipe_id: recipeId,
      status: RecipeResourceStatus.active,
      updated_by: userId,
      updated_at: new Date()
    },
    {
      where: {
        id: {
          [Op.in]: tempFiles.map((file: any) => file.id)
        }
      },
      transaction,
    }
  );

  return {
    success: true,
    updatedFiles: updatedCount[0],
    message: `Successfully associated ${updatedCount[0]} files with recipe`
  };
};

// ============================================================================
// API 1: BASIC RECIPE INFORMATION
// Handles: basic recipe info, categories, dietary_attributes
// ============================================================================

/**
 * @description Handle the basic information step of recipe creation
 * @route POST /api/v1/recipes/batch/basic-info
 * @access Private
 * @functionality Creates new recipe with basic info, categories, and dietary attributes
 */
const createRecipeBasicInfo = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract basic recipe information fields
    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_complexity_level,
      categories,
      dietary_attributes
    } = sanitizedBody;


    const { id, organization_id } = req.user;
    const userId = id;
    const organizationId = organization_id;

    // Validate required fields
    if (!recipe_title) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe title is required",
      });
    }

    // Generate unique slug from recipe title
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existingRecipe = await Recipe.findOne({
        where: {
          recipe_slug: slug,
          organization_id: organization_id,
          recipe_status: {
            [Op.not]: RecipeStatus.deleted,
          },
        },
      });
      return !!existingRecipe;
    };

    // Generate unique slug
    const recipe_slug = await generateUniqueSlug(
      recipe_title,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Create main recipe with initial timestamps
    const currentTimestamp = new Date();
    const recipeData = {
      // Basic info
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility: has_recipe_public_visibility || false,
      has_recipe_private_visibility: has_recipe_private_visibility || false,
      recipe_status: recipe_status || RecipeStatus.draft,
      recipe_complexity_level,
      recipe_slug,
      ingredient_costs_updated_at: currentTimestamp,
      nutrition_values_updated_at: currentTimestamp,
      organization_id: organization_id,
      created_by: userId,
      updated_by: userId,
    };

    const newRecipe = await Recipe.create(recipeData, { transaction });

    // Add categories if provided
    if (categories && Array.isArray(categories) && categories.length > 0) {
      const categoryData = categories.map((categoryId: number) => ({
        recipe_id: newRecipe.id,
        category_id: categoryId,
        status: "active",
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await db.RecipeCategory.bulkCreate(categoryData, { transaction });
    }



    // Create recipe attributes (optional for draft) - Type-wise handling
    const allAttributeData: any[] = [];

    // Handle dietary attributes (simple IDs)
    if (dietary_attributes && dietary_attributes.length > 0) {
      const dietaryData = dietary_attributes.map((attrId: number) => ({
        recipe_id: newRecipe.id,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...dietaryData);
    }
    // Bulk create all attributes if any exist
    if (allAttributeData.length > 0) {
      await RecipeAttributes.bulkCreate(allAttributeData, { transaction });
    }

    // Create recipe history entry for basic info
    await createRecipeHistory({
      recipe_id: newRecipe.id,
      action: RecipeHistoryAction.created,
      description: `Recipe "${recipe_title}" was created with basic information.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Recipe basic information saved successfully",
      data: {
        recipe_id: newRecipe.id,
        recipe_slug: newRecipe.recipe_slug,
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating recipe basic information"
    );
  }
};

// ============================================================================
// API 2: INGREDIENTS, NUTRITION & SERVING DETAILS
// Handles: ingredients, allergens, nutritions, HACCP data, cuisine attributes, serving details
// ============================================================================

/**
 * @description Handle ingredients, nutrition, cuisine type data and serving details for a recipe
 * @route POST /api/v1/recipes/batch/ingredients-nutrition
 * @access Private
 * @functionality Updates recipe with ingredients, nutrition, allergens, cuisine, HACCP data, and serving details
 */
const addIngredientsNutritionCuisine = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract ingredients, nutrition data, and serving details
    const {
      recipe_id,
      ingredients,
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      haccp_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
      // Serving details
      recipe_serve_in,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method
    } = sanitizedBody;

    // Authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (authResult.error) return authResult.error;

    const { userId, organizationId } = authResult;

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    await Recipe.update(
      {
        is_ingredient_cooking_method:
          is_ingredient_cooking_method === "true" ||
          is_ingredient_cooking_method === true,
        is_preparation_method:
          is_preparation_method === "true" || is_preparation_method === true,
        is_cost_manual:
          is_cost_manual === "true" || is_cost_manual === true,
      },
      {
        where: { id: recipe_id },
        transaction,
      }
    );

    // Add ingredients if provided
    if (ingredients && ingredients.length > 0) {
      // First make any existing ingredients inactive
      await RecipeIngredients.update(
        { recipe_ingredient_status: RecipeIngredientsStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      // Create new ingredient records
      const ingredientData = ingredients.map((ingredient: any) => ({
        recipe_id,
        ingredient_id: ingredient.id,
        ingredient_quantity: ingredient.quantity,
        ingredient_measure: ingredient.measure,
        ingredient_wastage: ingredient.wastage,
        ingredient_cost: ingredient.cost,
        ingredient_cooking_method: ingredient.cooking_method,
        preparation_method: ingredient.preparation_method,
        recipe_ingredient_status: RecipeIngredientsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await RecipeIngredients.bulkCreate(ingredientData, { transaction });

      // Update ingredient costs timestamp
      await updateRecipeCostTimestamp(recipe_id, transaction);
    }

    // Add nutrition attributes if provided
    if (
      nutrition_attributes && nutrition_attributes.length > 0 ||
      allergen_attributes ||
      cuisine_attributes ||
      dietary_attributes ||
      haccp_attributes
    ) {
      // First make any existing attributes inactive
      await RecipeAttributes.update(
        { status: RecipeAttributesStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      const attributesData = [];

      // Add nutrition attributes
      if (nutrition_attributes && nutrition_attributes.length > 0) {
        const nutritionData = nutrition_attributes.map((attr: any) => ({
          recipe_id,
          attributes_id: attr.id,
          unit_of_measure: attr.unit_of_measure,
          unit: attr.unit,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          attribute_type: "nutrition",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...nutritionData);
      }

      // Add allergen "contains" attributes
      if (allergen_attributes?.contains && allergen_attributes.contains.length > 0) {
        const allergenContains = allergen_attributes.contains.map((attrId: any) => ({
          recipe_id,
          attributes_id: attrId,
          attribute_type: "allergen_contains",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...allergenContains);
      }

      // Add allergen "may contain" attributes
      if (allergen_attributes?.may_contain && allergen_attributes.may_contain.length > 0) {
        const allergenMayContain = allergen_attributes.may_contain.map((attrId: any) => ({
          recipe_id,
          attributes_id: attrId,
          attribute_type: "allergen_may_contain",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...allergenMayContain);
      }

      // Add cuisine attributes
      if (cuisine_attributes && cuisine_attributes.length > 0) {
        const cuisineData = cuisine_attributes.map((attrId: any) => ({
          recipe_id,
          attributes_id: attrId,
          attribute_type: "cuisine",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...cuisineData);
      }

      // Add dietary attributes
      if (dietary_attributes && dietary_attributes.length > 0) {
        const dietaryData = dietary_attributes.map((attrId: any) => ({
          recipe_id,
          attributes_id: attrId,
          attribute_type: "dietary",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...dietaryData);
      }

      // Add HACCP attributes
      if (haccp_attributes && haccp_attributes.length > 0) {
        const haccpData = haccp_attributes.map((attr: any) => ({
          recipe_id,
          attributes_id: attr.id,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          attribute_type: "haccp",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...haccpData);
      }

      // Bulk create all attributes
      if (attributesData.length > 0) {
        await RecipeAttributes.bulkCreate(attributesData, { transaction });
      }

      // Update nutrition values timestamp
      await updateRecipeNutritionTimestamp(recipe_id, transaction);
    }

    // Update recipe with serving details
    const updateData: any = {
      updated_by: userId,
      updated_at: new Date()
    };

    // Add serving details if provided
    if (recipe_serve_in !== undefined) updateData.recipe_serve_in = recipe_serve_in;
    if (recipe_garnish !== undefined) updateData.recipe_garnish = recipe_garnish;
    if (recipe_head_chef_tips !== undefined) updateData.recipe_head_chef_tips = recipe_head_chef_tips;
    if (recipe_foh_tips !== undefined) updateData.recipe_foh_tips = recipe_foh_tips;
    if (recipe_impression !== undefined) updateData.recipe_impression = recipe_impression;
    if (recipe_yield !== undefined) updateData.recipe_yield = recipe_yield;
    if (recipe_yield_unit !== undefined) updateData.recipe_yield_unit = recipe_yield_unit;
    if (recipe_total_portions !== undefined) updateData.recipe_total_portions = recipe_total_portions;
    if (recipe_single_portion_size !== undefined) updateData.recipe_single_portion_size = recipe_single_portion_size;
    if (recipe_serving_method !== undefined) updateData.recipe_serving_method = recipe_serving_method;

    await recipe.update(updateData, { transaction });

    // Create recipe history entry
    await createRecipeHistory({
      recipe_id,
      action: RecipeHistoryAction.updated,
      description: `Recipe "${recipe.recipe_title}" ingredients, nutrition, cuisine types, and serving details were updated.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe ingredients, nutrition, cuisine data, and serving details saved successfully",
      data: { recipe_id },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding ingredients, nutrition data, and serving details"
    );
  }
};

// ============================================================================
// API 4: RECIPE STEPS MANAGEMENT
// Handles: recipe steps creation and updates
// ============================================================================

/**
 * @description Add or update recipe steps
 * @route POST /api/v1/recipes/batch/steps
 * @access Private
 * @functionality Processes all recipe steps at once
 */
const addRecipeSteps = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract steps data
    const {
      recipe_id,
      steps
    } = sanitizedBody;

    // Authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (authResult.error) return authResult.error;

    const { userId, organizationId } = authResult;

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Validate steps data
    if (!Array.isArray(steps) || steps.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Steps array is required and must contain at least one step",
      });
    }

    // Make all existing steps inactive
    await RecipeSteps.update(
      { status: RecipeStepsStatus.inactive },
      {
        where: { recipe_id },
        transaction,
      }
    );

    // Create new step records
    const stepsData = steps.map((step: any, index: number) => ({
      recipe_id,
      recipe_step_order: step.order || index + 1, // Use provided order or sequential numbering
      recipe_step_description: step.description || step.step_description || "",
      item_id: step.item_id || null, // Optional item reference
      status: RecipeStepsStatus.active,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));

    await RecipeSteps.bulkCreate(stepsData, { transaction });

    // Update recipe
    await recipe.update(
      {
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Create history entry
    await createRecipeHistory({
      recipe_id,
      action: RecipeHistoryAction.updated,
      description: `Recipe "${recipe.recipe_title}" steps were updated.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe steps saved successfully",
      data: {
        recipe_id,
        steps_added: steps.length,
        total_steps: steps.length
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding recipe steps"
    );
  }
};

// ============================================================================
// API 3: RECIPE FILE ASSOCIATION
// Handles: associating uploaded files with recipes
// ============================================================================

/**
 * @description Associate uploaded files with a recipe
 * @route POST /api/v1/recipes/batch/uploads
 * @access Private
 * @functionality Associates pre-uploaded files with a recipe
 */
const addRecipeUploads = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    // Parse form data
    const recipe_id = req.body.recipe_id;
    const fileIds = req.body.file_ids ?
      (Array.isArray(req.body.file_ids) ? req.body.file_ids : JSON.parse(req.body.file_ids)) :
      [];

    // Authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (authResult.error) return authResult.error;

    const { userId, organizationId } = authResult;

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Validate file IDs provided
    if (!fileIds || fileIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "file_ids array is required and must contain at least one file ID",
      });
    }

    // Make all existing resources inactive
    await RecipeResources.update(
      { status: RecipeResourceStatus.inactive },
      {
        where: { recipe_id },
        transaction,
      }
    );

    // Associate pre-uploaded files with recipe
    const associationResult = await associateFilesWithRecipe(
      recipe_id,
      fileIds,
      organizationId,
      userId,
      transaction
    );

    if (!associationResult.success) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: associationResult.message,
      });
    }

    // Update recipe
    await recipe.update(
      {
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Create history entry
    await createRecipeHistory({
      recipe_id,
      action: RecipeHistoryAction.updated,
      description: `Recipe "${recipe.recipe_title}" files were associated.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    // Commit transaction
    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe files associated successfully",
      data: {
        recipe_id,
        files_associated: associationResult.updatedFiles,
        total_files: associationResult.updatedFiles
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error associating recipe files"
    );
  }
};

// ============================================================================
// API 5: SINGLE FILE UPLOAD FOR BATCH PROCESSING
// Handles: individual file upload and returns file ID for frontend
// ============================================================================

/**
 * @description Upload a single file for batch recipe processing
 * @route POST /api/v1/recipes/batch/upload-file
 * @access Private
 * @functionality Uploads a single file and returns file details for frontend to use
 */
const uploadSingleFile = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();
  const fileTracker = new FileOperationTracker();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    // Authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (authResult.error) return authResult.error;

    const { userId, organizationId } = authResult;

    // Check if file was uploaded
    const file = req.file;
    if (!file) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No file uploaded",
      });
    }

    // Generate unique filename
    const uniqueFilename = `${Date.now()}-${file.originalname.replace(/\s+/g, "-")}`;
    const bucketName = process.env.NODE_ENV || "development";

    // Determine file path based on organization
    const orgName = organizationId ? organizationId.toString() : null;
    const filePath = orgName
      ? `${orgName}/recipe_batch_files/${uniqueFilename}`
      : `recipe_defaults/recipe_batch_files/${uniqueFilename}`;

    // Upload the file to S3 bucket
    const uploadResult = await uploadService.uploadFileToBucket(
      bucketName,
      filePath,
      file.buffer,
      file.mimetype
    );

    if (!uploadResult.success) {
      throw new Error(`Failed to upload file: ${file.originalname}`);
    }

    // Track file creation for potential rollback
    fileTracker.trackCreate(filePath);

    // Create a temporary file record in database
    const fileRecord = await RecipeResources.create({
      recipe_id: null, // Will be set later when recipe is created/updated
      resource_title: file.originalname,
      resource_type: file.mimetype.startsWith("image/") ? "image" : "document",
      resource_key: filePath,
      resource_url: `${global.config.API_BASE_URL}/${filePath}`,
      resource_size: file.size,
      resource_order: 1,
      status: "temporary", // Mark as temporary until associated with recipe
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }, { transaction });

    // Commit transaction
    await transactionManager.commit();
    fileTracker.clear();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "File uploaded successfully",
      data: {
        file_id: fileRecord.id,
        original_name: file.originalname,
        file_name: uniqueFilename,
        file_size: file.size,
        mime_type: file.mimetype,
        file_path: filePath,
        resource_url: fileRecord.resource_url,
        resource_type: fileRecord.resource_type
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    await fileTracker.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error uploading file"
    );
  }
};

// ============================================================================
// API 6: DELETE UPLOADED FILE
// Handles: deletion of uploaded files when user clicks cross button
// ============================================================================

/**
 * @description Delete an uploaded file by ID
 * @route DELETE /api/v1/recipes/batch/delete-file/:fileId
 * @access Private
 * @functionality Deletes uploaded file from storage and database
 */
const deleteUploadedFile = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    const fileId = req.params.fileId;

    // Authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (authResult.error) return authResult.error;

    const { userId, organizationId } = authResult;

    // Find the file record
    const fileRecord = await RecipeResources.findOne({
      where: {
        id: fileId,
        organization_id: organizationId,
        status: {
          [Op.in]: ["temporary", RecipeResourceStatus.active, RecipeResourceStatus.inactive]
        }
      },
      transaction,
    });

    if (!fileRecord) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "File not found or you don't have permission to delete it",
      });
    }

    // Delete file from S3/MinIO storage
    const bucketName = process.env.NODE_ENV || "development";
    try {
      await uploadService.deleteFileFromBucket(bucketName, fileRecord.resource_key);
    } catch (storageError) {
      console.warn(`Failed to delete file from storage: ${fileRecord.resource_key}`, storageError);
      // Continue with database deletion even if storage deletion fails
    }

    // Delete the database record
    await fileRecord.destroy({ transaction });

    // Commit transaction
    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "File deleted successfully",
      data: {
        deleted_file_id: fileId,
        original_name: fileRecord.resource_title
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting file"
    );
  }
};

// ============================================================================
// API 7: BULK DELETE UPLOADED FILES
// Handles: deletion of multiple uploaded files when user discards work
// ============================================================================

/**
 * @description Delete multiple uploaded files in bulk (for discard functionality)
 * @route DELETE /api/v1/recipes/batch/delete-files
 * @access Private
 * @functionality Deletes multiple uploaded files when user discards their work
 */
const bulkDeleteUploadedFiles = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    const { file_ids } = req.body;

    // Authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (authResult.error) return authResult.error;

    const { userId, organizationId } = authResult;

    // Validate file IDs
    if (!file_ids || !Array.isArray(file_ids) || file_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "file_ids array is required and must contain at least one file ID",
      });
    }

    // Find the file records that belong to the user's organization and are temporary
    const fileRecords = await RecipeResources.findAll({
      where: {
        id: {
          [Op.in]: file_ids
        },
        organization_id: organizationId,
        status: "temporary", // Only allow deletion of temporary files
        recipe_id: null // Only files not yet associated with recipes
      },
      transaction,
    });

    if (fileRecords.length === 0) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "No valid temporary files found to delete",
      });
    }

    const bucketName = process.env.NODE_ENV || "development";
    const deletedFiles: any[] = [];
    const failedDeletions: any[] = [];

    // Delete files from storage and track results
    for (const fileRecord of fileRecords) {
      try {
        await uploadService.deleteFileFromBucket(bucketName, fileRecord.resource_key);
        deletedFiles.push({
          file_id: fileRecord.id,
          file_name: fileRecord.resource_title
        });
      } catch (storageError) {
        console.warn(`Failed to delete file from storage: ${fileRecord.resource_key}`, storageError);
        failedDeletions.push({
          file_id: fileRecord.id,
          file_name: fileRecord.resource_title,
          error: "Storage deletion failed"
        });
      }
    }

    // Delete all file records from database (even if storage deletion failed)
    await RecipeResources.destroy({
      where: {
        id: {
          [Op.in]: fileRecords.map((file: any) => file.id)
        }
      },
      transaction,
    });

    // Commit transaction
    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: `Successfully deleted ${fileRecords.length} temporary files`,
      data: {
        total_files_requested: file_ids.length,
        files_found: fileRecords.length,
        files_deleted: fileRecords.length,
        storage_deletions_successful: deletedFiles.length,
        storage_deletions_failed: failedDeletions.length,
        deleted_files: deletedFiles,
        failed_deletions: failedDeletions.length > 0 ? failedDeletions : undefined
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting multiple files"
    );
  }
};

// ============================================================================
// EXPORTS - FOCUSED BATCH API ENDPOINTS
// ============================================================================

/**
 * Refactored Recipe Batch Controller
 *
 * This controller has been organized into 7 focused API endpoints:
 *
 * 1. createRecipeBasicInfo - API 1: Basic recipe information, categories, dietary attributes
 * 2. addIngredientsNutritionCuisine - API 2: Ingredients, allergens, nutrition, HACCP data, cuisine attributes, serving details
 * 3. addRecipeSteps - API 4: Recipe steps management (simplified)
 * 4. addRecipeUploads - API 3: File association with recipes (simplified)
 * 5. uploadSingleFile - API 5: Single file upload for individual file handling
 * 6. deleteUploadedFile - API 6: Delete uploaded files
 * 7. bulkDeleteUploadedFiles - API 7: Bulk delete temporary files (for discard functionality)
 *
 * Key Features Maintained:
 * - All existing middleware (auth, validation, CORS, rate limiting)
 * - Same response formats and error handling patterns
 * - Transaction management and rollback capabilities
 * - File operation tracking and cleanup
 * - Backward compatibility with existing endpoints
 *
 * Improvements:
 * - Simplified APIs without complex batch logic
 * - Better separation of concerns
 * - Shared utility functions for common operations
 * - Consistent authorization and validation patterns
 * - Clear section organization with descriptive headers
 * - Reduced code duplication
 * - Individual file upload and delete capabilities
 * - Simplified file association process
 */
export default {
  createRecipeBasicInfo,
  addIngredientsNutritionCuisine,
  addRecipeSteps,
  addRecipeUploads,
  uploadSingleFile,
  deleteUploadedFile,
  bulkDeleteUploadedFiles
};
