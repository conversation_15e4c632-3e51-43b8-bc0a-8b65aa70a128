#!/usr/bin/env ts-node

/**
 * Standalone Default Icon Seeder
 * Run this script to upload all default icons for categories and allergens
 *
 * Usage:
 * npx ts-node src/scripts/seedDefaultIcons.ts
 *
 * Or with specific type:
 * npx ts-node src/scripts/seedDefaultIcons.ts --type=recipe-categories
 * npx ts-node src/scripts/seedDefaultIcons.ts --type=ingredient-categories
 * npx ts-node src/scripts/seedDefaultIcons.ts --type=allergens
 * npx ts-node src/scripts/seedDefaultIcons.ts --type=all
 */

import { QueryTypes } from "sequelize";
import iconSeederService from "../services/iconSeeder.service";
import { db } from "../models/index";

// Parse command line arguments
const args = process.argv.slice(2);
const typeArg = args.find((arg) => arg.startsWith("--type="));
const typeInput = typeArg ? typeArg.split("=")[1] : "all";
const types = typeInput.includes(",") ? typeInput.split(",") : [typeInput];

console.log(`🚀 Starting Default Icon Seeder for: ${typeInput}`);
console.log("=".repeat(60));

async function seedRecipeCategoryIcons() {
  console.log("\n📋 Processing Recipe Category Icons...");

  const categories = await db.sequelize.query(
    `SELECT category_name, category_icon FROM mo_category WHERE category_type = 'recipe' AND organization_id IS NULL AND is_system_category = true`,
    { type: QueryTypes.SELECT }
  );

  const iconMapping = iconSeederService.getRecipeCategoryIconMapping();
  const results = [];

  for (const category of categories as any[]) {
    const iconFileName = iconMapping[category.category_name];

    if (!iconFileName) {
      console.log(`⚠️  ${category.category_name}: No icon mapping found`);
      results.push({
        name: category.category_name,
        status: "skipped",
        reason: "No icon mapping",
      });
      continue;
    }

    if (category.category_icon) {
      console.log(`⏭️  ${category.category_name}: Already has icon`);
      results.push({
        name: category.category_name,
        status: "skipped",
        reason: "Already has icon",
      });
      continue;
    }

    try {
      console.log(`📤 ${category.category_name}: Uploading ${iconFileName}...`);
      const iconId = await iconSeederService.uploadRecipeCategoryIcon(
        category.category_name,
        iconFileName
      );

      if (iconId) {
        await db.sequelize.query(
          `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'recipe' AND organization_id IS NULL`,
          {
            replacements: { iconId, categoryName: category.category_name },
            type: QueryTypes.UPDATE,
          }
        );

        console.log(`✅ ${category.category_name}: Success (ID: ${iconId})`);
        results.push({
          name: category.category_name,
          status: "success",
          iconId,
          fileName: iconFileName,
        });
      } else {
        console.log(`❌ ${category.category_name}: Upload failed`);
        results.push({
          name: category.category_name,
          status: "failed",
          reason: "Upload failed",
        });
      }
    } catch (error: any) {
      console.log(`❌ ${category.category_name}: Error - ${error.message}`);
      results.push({
        name: category.category_name,
        status: "error",
        reason: error.message,
      });
    }
  }

  return results;
}

async function seedIngredientCategoryIcons() {
  console.log("\n📋 Processing Ingredient Category Icons...");

  const categories = await db.sequelize.query(
    `SELECT category_name, category_icon FROM mo_category WHERE category_type = 'ingredient' AND organization_id IS NULL AND is_system_category = true`,
    { type: QueryTypes.SELECT }
  );

  const iconMapping = iconSeederService.getIngredientCategoryIconMapping();
  const results = [];

  for (const category of categories as any[]) {
    const iconFileName = iconMapping[category.category_name];

    if (!iconFileName) {
      console.log(`⚠️  ${category.category_name}: No icon mapping found`);
      results.push({
        name: category.category_name,
        status: "skipped",
        reason: "No icon mapping",
      });
      continue;
    }

    if (category.category_icon) {
      console.log(`⏭️  ${category.category_name}: Already has icon`);
      results.push({
        name: category.category_name,
        status: "skipped",
        reason: "Already has icon",
      });
      continue;
    }

    try {
      console.log(`📤 ${category.category_name}: Uploading ${iconFileName}...`);
      const iconId = await iconSeederService.uploadIngredientCategoryIcon(
        category.category_name,
        iconFileName
      );

      if (iconId) {
        await db.sequelize.query(
          `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'ingredient' AND organization_id IS NULL`,
          {
            replacements: { iconId, categoryName: category.category_name },
            type: QueryTypes.UPDATE,
          }
        );

        console.log(`✅ ${category.category_name}: Success (ID: ${iconId})`);
        results.push({
          name: category.category_name,
          status: "success",
          iconId,
          fileName: iconFileName,
        });
      } else {
        console.log(`❌ ${category.category_name}: Upload failed`);
        results.push({
          name: category.category_name,
          status: "failed",
          reason: "Upload failed",
        });
      }
    } catch (error: any) {
      console.log(`❌ ${category.category_name}: Error - ${error.message}`);
      results.push({
        name: category.category_name,
        status: "error",
        reason: error.message,
      });
    }
  }

  return results;
}

async function seedAllergenIcons() {
  console.log("\n📋 Processing Allergen Icons...");

  const allergens = await db.sequelize.query(
    `SELECT attribute_title, attribute_icon FROM mo_food_attributes WHERE attribute_type = 'allergen' AND organization_id IS NULL AND is_system_attribute = true`,
    { type: QueryTypes.SELECT }
  );

  const iconMapping = iconSeederService.getAllergenIconMapping();
  const results = [];

  for (const allergen of allergens as any[]) {
    const iconFileName = iconMapping[allergen.attribute_title];

    if (!iconFileName) {
      console.log(`⚠️  ${allergen.attribute_title}: No icon mapping found`);
      results.push({
        name: allergen.attribute_title,
        status: "skipped",
        reason: "No icon mapping",
      });
      continue;
    }

    if (allergen.attribute_icon) {
      console.log(`⏭️  ${allergen.attribute_title}: Already has icon`);
      results.push({
        name: allergen.attribute_title,
        status: "skipped",
        reason: "Already has icon",
      });
      continue;
    }

    try {
      console.log(
        `📤 ${allergen.attribute_title}: Uploading ${iconFileName}...`
      );
      const iconId = await iconSeederService.uploadAttributeIcon(
        allergen.attribute_title,
        iconFileName,
        "src/icons/Allergens"
      );

      if (iconId) {
        await db.sequelize.query(
          `UPDATE mo_food_attributes SET attribute_icon = :iconId WHERE attribute_title = :attributeName AND attribute_type = 'allergen' AND organization_id IS NULL`,
          {
            replacements: { iconId, attributeName: allergen.attribute_title },
            type: QueryTypes.UPDATE,
          }
        );

        console.log(`✅ ${allergen.attribute_title}: Success (ID: ${iconId})`);
        results.push({
          name: allergen.attribute_title,
          status: "success",
          iconId,
          fileName: iconFileName,
        });
      } else {
        console.log(`❌ ${allergen.attribute_title}: Upload failed`);
        results.push({
          name: allergen.attribute_title,
          status: "failed",
          reason: "Upload failed",
        });
      }
    } catch (error: any) {
      console.log(`❌ ${allergen.attribute_title}: Error - ${error.message}`);
      results.push({
        name: allergen.attribute_title,
        status: "error",
        reason: error.message,
      });
    }
  }

  return results;
}

async function main() {
  try {
    let allResults: any[] = [];

    // Process based on types
    if (types.includes("all") || types.includes("recipe-categories")) {
      const recipeResults = await seedRecipeCategoryIcons();
      allResults = allResults.concat(recipeResults);
    }

    if (types.includes("all") || types.includes("ingredient-categories")) {
      const ingredientResults = await seedIngredientCategoryIcons();
      allResults = allResults.concat(ingredientResults);
    }

    if (types.includes("all") || types.includes("allergens")) {
      const allergenResults = await seedAllergenIcons();
      allResults = allResults.concat(allergenResults);
    }

    // Print summary
    console.log("\n" + "=".repeat(60));
    console.log("📊 SUMMARY");
    console.log("=".repeat(60));

    const summary = {
      total: allResults.length,
      success: allResults.filter((r) => r.status === "success").length,
      skipped: allResults.filter((r) => r.status === "skipped").length,
      failed: allResults.filter(
        (r) => r.status === "failed" || r.status === "error"
      ).length,
    };

    console.log(`Total: ${summary.total}`);
    console.log(`✅ Success: ${summary.success}`);
    console.log(`⏭️  Skipped: ${summary.skipped}`);
    console.log(`❌ Failed: ${summary.failed}`);

    if (summary.failed > 0) {
      console.log("\n❌ Failed items:");
      allResults
        .filter((r) => r.status === "failed" || r.status === "error")
        .forEach((item) => {
          console.log(`   - ${item.name}: ${item.reason}`);
        });
    }

    console.log("\n🎉 Icon seeding completed!");
    process.exit(0);
  } catch (error: any) {
    console.error("\n💥 Fatal error:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the seeder
main();
