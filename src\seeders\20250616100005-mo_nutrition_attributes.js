const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if nutrition attributes already exist
    const existingNutrition = await queryInterface.sequelize.query(
      `SELECT * FROM mo_food_attributes WHERE organization_id IS NULL AND is_system_attribute = true AND attribute_type = 'nutrition'`,
      { type: QueryTypes.SELECT }
    );

    if (existingNutrition.length === 0) {
      // Nutrition Attributes
      const nutritionAttributes = [
        // Macronutrients
        {
          name: "Calories",
          slug: "calories",
          description: "Total energy content measured in kilocalories (kcal)",
        },
        {
          name: "Protein",
          slug: "protein",
          description: "Essential macronutrient for muscle building and repair",
        },
        {
          name: "Carbohydrates",
          slug: "carbohydrates",
          description: "Primary energy source for the body",
        },
        {
          name: "Total Fat",
          slug: "total-fat",
          description: "Essential macronutrient including all types of fats",
        },
        {
          name: "Saturated Fat",
          slug: "saturated-fat",
          description: "Type of fat that is solid at room temperature",
        },
        {
          name: "Trans Fat",
          slug: "trans-fat",
          description:
            "Artificially created fat that should be limited in diet",
        },
        {
          name: "Monounsaturated Fat",
          slug: "monounsaturated-fat",
          description: "Healthy fat found in olive oil, nuts, and avocados",
        },
        {
          name: "Polyunsaturated Fat",
          slug: "polyunsaturated-fat",
          description: "Essential fatty acids including omega-3 and omega-6",
        },
        {
          name: "Dietary Fiber",
          slug: "dietary-fiber",
          description: "Indigestible carbohydrate that aids digestion",
        },
        {
          name: "Sugar",
          slug: "sugar",
          description:
            "Simple carbohydrates including natural and added sugars",
        },
        {
          name: "Added Sugar",
          slug: "added-sugar",
          description: "Sugars added during processing or preparation",
        },

        // Minerals
        {
          name: "Sodium",
          slug: "sodium",
          description: "Essential mineral that regulates fluid balance",
        },
        {
          name: "Potassium",
          slug: "potassium",
          description: "Mineral important for heart and muscle function",
        },
        {
          name: "Calcium",
          slug: "calcium",
          description: "Mineral essential for bone and teeth health",
        },
        {
          name: "Iron",
          slug: "iron",
          description: "Mineral necessary for oxygen transport in blood",
        },
        {
          name: "Magnesium",
          slug: "magnesium",
          description: "Mineral involved in over 300 enzymatic reactions",
        },
        {
          name: "Phosphorus",
          slug: "phosphorus",
          description:
            "Mineral important for bone health and energy metabolism",
        },
        {
          name: "Zinc",
          slug: "zinc",
          description:
            "Mineral essential for immune function and wound healing",
        },
        {
          name: "Copper",
          slug: "copper",
          description: "Trace mineral important for iron absorption",
        },
        {
          name: "Manganese",
          slug: "manganese",
          description:
            "Trace mineral involved in bone formation and metabolism",
        },
        {
          name: "Selenium",
          slug: "selenium",
          description: "Antioxidant mineral that supports immune function",
        },

        // Vitamins
        {
          name: "Vitamin A",
          slug: "vitamin-a",
          description:
            "Fat-soluble vitamin important for vision and immune function",
        },
        {
          name: "Vitamin C",
          slug: "vitamin-c",
          description: "Water-soluble vitamin and powerful antioxidant",
        },
        {
          name: "Vitamin D",
          slug: "vitamin-d",
          description: "Fat-soluble vitamin essential for bone health",
        },
        {
          name: "Vitamin E",
          slug: "vitamin-e",
          description: "Fat-soluble antioxidant vitamin",
        },
        {
          name: "Vitamin K",
          slug: "vitamin-k",
          description: "Fat-soluble vitamin important for blood clotting",
        },
        {
          name: "Thiamine (B1)",
          slug: "thiamine-b1",
          description: "Water-soluble vitamin essential for energy metabolism",
        },
        {
          name: "Riboflavin (B2)",
          slug: "riboflavin-b2",
          description: "Water-soluble vitamin important for energy production",
        },
        {
          name: "Niacin (B3)",
          slug: "niacin-b3",
          description:
            "Water-soluble vitamin that supports nervous system function",
        },
        {
          name: "Pantothenic Acid (B5)",
          slug: "pantothenic-acid-b5",
          description: "Water-soluble vitamin involved in energy metabolism",
        },
        {
          name: "Pyridoxine (B6)",
          slug: "pyridoxine-b6",
          description: "Water-soluble vitamin important for protein metabolism",
        },
        {
          name: "Biotin (B7)",
          slug: "biotin-b7",
          description: "Water-soluble vitamin essential for metabolism",
        },
        {
          name: "Folate (B9)",
          slug: "folate-b9",
          description: "Water-soluble vitamin crucial for DNA synthesis",
        },
        {
          name: "Cobalamin (B12)",
          slug: "cobalamin-b12",
          description: "Water-soluble vitamin essential for nerve function",
        },

        // Other Important Nutrients
        {
          name: "Cholesterol",
          slug: "cholesterol",
          description: "Waxy substance found in animal products",
        },
        {
          name: "Omega-3 Fatty Acids",
          slug: "omega-3-fatty-acids",
          description:
            "Essential fatty acids with anti-inflammatory properties",
        },
        {
          name: "Omega-6 Fatty Acids",
          slug: "omega-6-fatty-acids",
          description: "Essential fatty acids important for brain function",
        },
      ];

      // Prepare bulk insert data
      const nutritionData = nutritionAttributes.map((nutrition) => ({
        attribute_title: nutrition.name,
        attribute_slug: nutrition.slug,
        attribute_description: nutrition.description,
        attribute_type: "nutrition",
        attribute_icon: null,
        attribute_status: "active",
        organization_id: null,
        is_system_attribute: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("mo_food_attributes", nutritionData);
      console.log("✅ Nutrition Attributes seeded successfully");
      console.log(
        "ℹ️  Note: Nutrition attribute icons are not available yet - consider adding icons to src/icons/nutrition/"
      );
    } else {
      console.log("⏭️  Nutrition Attributes already exist, skipping...");
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_food_attributes", {
      organization_id: null,
      is_system_attribute: true,
      attribute_type: "nutrition",
    });
  },
};
