import express from 'express';
import recipeBatchController from '../../controller/recipe-batch.controller';
import auth from '../../middleware/auth';
import upload, { enhancedUpload } from '../../middleware/upload';
import batchValidator from '../../validators/batch.validator';

const router = express.Router();

// Basic recipe information API
router.post('/basic-info',
  batchValidator.validateBasicInfoBatch(),
  recipeBatchController.createRecipeBasicInfo
);

// Ingredients, nutrition, and cuisine data API
router.post('/ingredients-nutrition',
  auth,
  batchValidator.validateIngredientsNutritionBatch(),
  recipeBatchController.addIngredientsNutritionCuisine
);

// Recipe steps batch API
router.post('/steps',
  auth,
  batchValidator.validateStepsBatch(),
  recipeBatchController.addRecipeSteps
);

// Recipe file association API
router.post('/uploads',
  auth,
  batchValidator.validateUploadsBatch(),
  recipeBatchController.addRecipeUploads
);

// Single file upload API - for individual file upload with file ID return
router.post('/upload-file',
  auth,
  batchValidator.validateSingleFileUpload(),
  enhancedUpload.single('file'),
  recipeBatchController.uploadSingleFile
);

// Delete uploaded file API - for removing uploaded files
router.delete('/delete-file/:fileId',
  auth,
  batchValidator.validateDeleteFile(),
  recipeBatchController.deleteUploadedFile
);

// Bulk delete uploaded files API - for discarding multiple files at once
router.delete('/delete-files',
  auth,
  batchValidator.validateBulkDeleteFiles(),
  recipeBatchController.bulkDeleteUploadedFiles
);

export default router;
