# Recipe Permission System Test Guide

## 🎯 Overview
This guide helps you test the enhanced permission-based access control system for your Recipe Microservice.

## 📋 Prerequisites
1. Your Recipe Microservice should be running (default: `http://localhost:3000`)
2. You need two JWT tokens:
   - **Super Admin Token**: A token for a user with super admin privileges
   - **Normal User Token**: A token for a regular user without admin privileges
3. You need existing data:
   - At least one recipe in your database
   - The normal user should exist in your system

## 🔧 Setup Instructions

### Step 1: Get Your Tokens
You can get JWT tokens by:
1. **Using Postman/API Client**: Make a login request to your authentication endpoint
2. **From Browser DevTools**: Login to your web app and copy the token from localStorage/cookies
3. **From Database**: If you have direct database access, you can generate tokens manually

### Step 2: Update Test Configuration
Open `test-recipe-permissions.js` and update the `CONFIG` section:

```javascript
const CONFIG = {
  BASE_URL: 'http://localhost:3000/api/v1', // Your server URL
  
  // 🔑 REPLACE WITH YOUR ACTUAL TOKENS
  ADMIN_TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', // Your super admin token
  NORMAL_USER_TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', // Your normal user token
  
  // 📝 REPLACE WITH ACTUAL IDs FROM YOUR DATABASE
  RECIPE_ID: '1', // An existing recipe ID
  NORMAL_USER_ID: '2', // The normal user's ID for assignment testing
  
  // 🎯 Test Settings
  TIMEOUT: 10000, // 10 seconds timeout
  VERBOSE: true, // Set to false for less output
};
```

### Step 3: Find Required IDs

#### Get Recipe ID:
```sql
SELECT id, recipe_title FROM mo_recipe LIMIT 5;
```

#### Get Normal User ID:
```sql
SELECT id, first_name, last_name FROM mo_user WHERE id NOT IN (
  SELECT user_id FROM mo_user_role WHERE role_id IN (
    SELECT id FROM mo_role WHERE role_name IN ('SUPER_ADMIN', 'ADMIN', 'DIRECTOR', 'HR')
  )
) LIMIT 5;
```

## 🚀 Running the Tests

### Method 1: Direct Node.js Execution
```bash
node test-recipe-permissions.js
```

### Method 2: With NPM Script (Optional)
Add to your `package.json`:
```json
{
  "scripts": {
    "test:permissions": "node test-recipe-permissions.js"
  }
}
```

Then run:
```bash
npm run test:permissions
```

## 📊 Understanding Test Results

### Test Scenarios Covered:

1. **Admin Recipe Access** ✅
   - Verifies super admin can access any recipe

2. **Normal User Blocked Access** ❌ (Expected to fail)
   - Confirms normal user cannot access unassigned recipes

3. **Admin Assign Recipe** ✅
   - Tests super admin's ability to assign recipes to users

4. **Normal User Assigned Access** ✅
   - Verifies normal user can access assigned recipes

5. **Normal User Assigned List** ✅
   - Tests the `/assigned-to-me` endpoint

6. **Normal User Cannot Manage Assignments** ❌ (Expected to fail)
   - Confirms normal users cannot manage recipe assignments

7. **Normal User Update Assigned Recipe** ✅
   - Tests normal user can modify assigned recipes

8. **Normal User Track Impression** ✅
   - Verifies impression tracking works for assigned recipes

9. **Admin Unassign Recipe** ✅
   - Tests super admin's ability to remove recipe assignments

10. **Normal User Loses Access After Unassignment** ❌ (Expected to fail)
    - Confirms access is revoked after unassignment

### Sample Output:
```
🎯 Recipe Permission System Test Suite
==================================================
🌐 Testing against: http://localhost:3000/api/v1
📝 Recipe ID: 1
👤 Normal User ID: 2

🧪 Running: Admin Recipe Access
✅ PASSED: Admin Recipe Access

🧪 Running: Normal User Blocked Access
✅ PASSED: Normal User Blocked Access

... (more tests)

============================================================
📊 TEST RESULTS SUMMARY
============================================================
Total Tests: 10
Passed: 10
Failed: 0
Pass Rate: 100.0%
============================================================
```

## 🔍 Troubleshooting

### Common Issues:

#### 1. "Please update the tokens in CONFIG section!"
- **Solution**: Replace the placeholder tokens with your actual JWT tokens

#### 2. "ECONNREFUSED" or Connection Errors
- **Solution**: Ensure your Recipe Microservice is running on the correct port
- Check the `BASE_URL` in the config

#### 3. "Invalid Token" or 401 Errors
- **Solution**: Verify your tokens are valid and not expired
- Check token format (should start with "Bearer " in Authorization header)

#### 4. "Recipe not found" or 404 Errors
- **Solution**: Ensure the `RECIPE_ID` exists in your database
- Use a recipe ID that actually exists

#### 5. "User not found" Errors
- **Solution**: Verify the `NORMAL_USER_ID` exists and is a regular user (not admin)

### Debug Mode:
Set `VERBOSE: true` in the config to see detailed request/response information.

## 🎯 Expected Behavior

### For Super Admin Users:
- ✅ Can access ANY recipe regardless of assignment
- ✅ Can assign/unassign recipes to/from users
- ✅ Can perform all recipe operations

### For Normal Users:
- ❌ Cannot access recipes not assigned to them
- ✅ Can access recipes assigned to them
- ✅ Can perform operations on assigned recipes
- ❌ Cannot manage recipe assignments
- ❌ Lose access when recipes are unassigned

## 📞 Support

If tests fail unexpectedly:
1. Check server logs for detailed error messages
2. Verify database connections are working
3. Ensure all middleware is properly applied to routes
4. Check that the permission system is correctly implemented

The test suite will exit with code 0 on success and code 1 on failure, making it suitable for CI/CD pipelines.
