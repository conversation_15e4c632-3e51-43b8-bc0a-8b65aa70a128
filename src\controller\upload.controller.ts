import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FileOperationTracker,
  TransactionManager,
} from "../helper/transaction.helper";
import { RECIPE_FILE_UPLOAD_CONSTANT } from "../helper/common";
import uploadService from "../helper/upload.service";
import { item_status } from "../models/Item";
import { Op, QueryTypes } from "sequelize";
import { db } from "../models";
import iconSeederService from "../services/iconSeeder.service";

const Item = db.Item;
// ============================================================================
// UPLOAD CONTROLLER
// Handles: Multiple file uploads for general use
// ============================================================================

/**
 * @description Upload multiple files for general use or recipe files with destination management
 * @route POST /api/v1/upload/files
 * @access Private
 * @functionality Uses upload service middleware to handle multiple file uploads, duplicate detection, and Item creation - returns array of item_ids
 */
const uploadMultipleFiles = async (
  req: Request,
  res: Response
): Promise<any> => {
  const fileTracker = new FileOperationTracker();

  try {
    // Check if files were processed by upload service middleware
    let uploadedFiles = req.files as any;

    // Handle different multer response formats
    if (!uploadedFiles) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          "No files uploaded or file processing failed. Make sure all files are sent with the field name 'file'.",
      });
    }

    // Convert to array if it's not already an array
    if (!Array.isArray(uploadedFiles)) {
      // If it's an object with file field, extract the array
      if (uploadedFiles.file && Array.isArray(uploadedFiles.file)) {
        uploadedFiles = uploadedFiles.file;
      } else if (typeof uploadedFiles === "object") {
        // Convert object to array
        uploadedFiles = Object.values(uploadedFiles);
      } else {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: "Invalid file format received from upload middleware.",
        });
      }
    }

    if (uploadedFiles.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          "No files uploaded or file processing failed. Make sure all files are sent with the field name 'file'.",
      });
    }

    // Log each file to see what properties it has
    uploadedFiles.forEach((file: any, index: number) => {
      console.log(`File ${index}:`, {
        originalname: file.originalname,
        filename: file.filename,
        path: file.path,
        size: file.size,
        mimetype: file.mimetype,
        item_id: file.item_id,
        type: file.type,
        isMovable: file.isMovable,
        hash: file.hash,
      });
    });

    // Check if this is a recipe file upload (has uploadType in body)
    const { uploadType, recipeId, stepNumbers } = req.body;
    const organizationId = req.user?.organization_id;
    const isRecipeUpload = uploadType && recipeId;

    if (isRecipeUpload) {
      // Recipe file upload with destination management
      const orgName = organizationId ? organizationId.toString() : null;
      const bucketName = process.env.NODE_ENV || "development";
      const processedFiles = [];

      for (let i = 0; i < uploadedFiles.length; i++) {
        const uploadedFile = uploadedFiles[i];

        if (!uploadedFile.item_id) {
          throw new Error(
            `File upload failed for ${uploadedFile.originalname} - no item ID generated`
          );
        }

        // Determine destination path based on upload type
        let destinationPath: string;
        let stepNumber = null;

        switch (uploadType) {
          case "recipePlaceholder":
          case "recipeImage":
            destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_IMAGE.destinationPath(
                orgName,
                recipeId,
                uploadedFile.filename
              );
            break;

          case "recipeFiles":
          case "recipeDocument":
            destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_DOCUMENT.destinationPath(
                orgName,
                recipeId,
                uploadedFile.filename
              );
            break;

          case "stepImages":
          case "stepMedia":
            // Get step number from array or use index + 1
            stepNumber = stepNumbers && stepNumbers[i] ? stepNumbers[i] : i + 1;
            destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_INSTRUCTION_MEDIA.destinationPath(
                orgName,
                recipeId,
                stepNumber,
                uploadedFile.filename
              );
            break;

          case "recipeVideo":
            destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_VIDEO.destinationPath(
                orgName,
                recipeId,
                uploadedFile.filename
              );
            break;

          case "recipeAudio":
            destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_AUDIO.destinationPath(
                orgName,
                recipeId,
                uploadedFile.filename
              );
            break;

          case "recipeThumbnail":
            destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_THUMBNAIL.destinationPath(
                orgName,
                recipeId,
                uploadedFile.filename
              );
            break;

          case "nutritionLabel":
            destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_NUTRITION_LABELS.destinationPath(
                orgName,
                recipeId,
                uploadedFile.filename
              );
            break;

          default:
            throw new Error(
              `Invalid uploadType: ${uploadType}. Supported types: recipePlaceholder, recipeFiles, stepImages, recipeVideo, recipeAudio, recipeThumbnail, nutritionLabel`
            );
        }

        // Move file to the correct location if it's movable (not a duplicate)
        if (uploadedFile.isMovable) {
          // Track file operation for potential rollback
          fileTracker.trackMove(
            uploadedFile.path,
            destinationPath,
            uploadedFile.item_id
          );

          const moveResult = await uploadService.moveFileInBucket(
            bucketName,
            uploadedFile.path,
            destinationPath,
            uploadedFile.item_id
          );

          if (!moveResult.success) {
            // Rollback file operations on error
            await fileTracker.rollback();
            throw new Error(
              `Failed to move file ${uploadedFile.originalname} to destination: ${moveResult.error}`
            );
          }
        }

        processedFiles.push({
          item_id: uploadedFile.item_id,
          file_name: uploadedFile.filename,
          recipe_id: recipeId,
          upload_type: uploadType,
          is_duplicate: !uploadedFile.isMovable,
        });
      }

      // Clear file tracker after successful operation
      fileTracker.clear();

      return res.status(StatusCodes.CREATED).json({
        status: true,
        message: `${processedFiles.length} recipe file(s) uploaded successfully`,
        data: {
          files: processedFiles,
          total_files: processedFiles.length,
          successful_uploads: processedFiles.length,
          upload_type: uploadType,
          recipe_id: recipeId,
        },
      });
    } else {
      // General file upload (backward compatibility)
      const processedFiles = uploadedFiles.map((uploadedFile: any) => {
        if (!uploadedFile.item_id) {
          throw new Error(
            `File upload failed for ${uploadedFile.originalname} - no item ID generated`
          );
        }

        return {
          item_id: uploadedFile.item_id,
          file_name: uploadedFile.filename,
          upload_type: uploadType,
          recipe_id: recipeId,
          is_duplicate: !uploadedFile.isMovable, // If not movable, it means it was a duplicate
        };
      });

      return res.status(StatusCodes.CREATED).json({
        status: true,
        message: `${processedFiles.length} file(s) uploaded successfully`,
        data: {
          files: processedFiles,
          total_files: processedFiles.length,
          successful_uploads: processedFiles.length,
        },
      });
    }
  } catch (error: unknown) {
    // Rollback file operations on any error
    try {
      await fileTracker.rollback();
    } catch (rollbackError) {
      console.error("Error during file rollback:", rollbackError);
    }

    console.error("Upload error:", error);
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error uploading files"
    );
  }
};

/**
 * @description Upload a single file for general use
 * @route POST /api/v1/upload/file
 * @access Private
 * @functionality Uses upload service middleware to handle file upload, duplicate detection, and Item creation - returns item_id
 */
const uploadSingleFile = async (req: Request, res: Response): Promise<any> => {
  try {
    // Check if file was processed by upload service middleware
    const files = req.files as { [fieldname: string]: any[] };
    if (!files || !files.file || files.file.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No file uploaded or file processing failed",
      });
    }

    // Extract file information from upload service middleware
    const uploadedFile = files.file[0]; // Get first (and only) file

    if (!uploadedFile.item_id) {
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        status: false,
        message: "File upload failed - no item ID generated",
      });
    }

    console.log(
      `File uploaded successfully: ${uploadedFile.originalname} -> ${uploadedFile.path} (${uploadedFile.type})`
    );

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "File uploaded successfully",
      data: {
        item_id: uploadedFile.item_id, // Return item_id from upload service
        original_name: uploadedFile.originalname,
        file_name: uploadedFile.filename,
        file_size: uploadedFile.size,
        mime_type: uploadedFile.mimetype,
        file_path: uploadedFile.path,
        file_url: `${global.config.API_BASE_URL}/${uploadedFile.path}`,
        item_type: uploadedFile.type,
        file_hash: uploadedFile.hash,
        is_duplicate: !uploadedFile.isMovable, // If not movable, it means it was a duplicate
      },
    });
  } catch (error: unknown) {
    return ErrorHandler.createErrorResponse(error, res, "Error uploading file");
  }
};

/**
 * @description Upload recipe file with dynamic path based on upload type
 * @route POST /api/v1/upload/recipe-file
 * @access Private
 * @functionality Uploads a single file and determines the correct path based on upload type from request body
 */
const uploadRecipeFile = async (req: Request, res: Response): Promise<any> => {
  const fileTracker = new FileOperationTracker();

  try {
    // Check if file was processed by upload service middleware
    const files = req.files as { [fieldname: string]: any[] };
    if (!files || !files.file || files.file.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No file uploaded or file processing failed",
      });
    }

    // Extract file information from upload service middleware
    const uploadedFile = files.file[0]; // Get first (and only) file

    if (!uploadedFile.item_id) {
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        status: false,
        message: "File upload failed - no item ID generated",
      });
    }

    // Get upload type and recipe information from request body
    const { uploadType, recipeId, stepNumber } = req.body;
    const organizationId = req.user?.organization_id;

    if (!uploadType) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "uploadType is required in request body",
      });
    }

    if (!recipeId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "recipeId is required in request body",
      });
    }

    // Determine the correct destination path based on upload type
    let destinationPath: string;
    const orgName = organizationId ? organizationId.toString() : null;
    const bucketName = process.env.NODE_ENV || "development";

    switch (uploadType) {
      case "recipePlaceholder":
      case "recipeImage":
        destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_IMAGE.destinationPath(
            orgName,
            recipeId,
            uploadedFile.filename
          );
        break;

      case "recipeFiles":
      case "recipeDocument":
        destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_DOCUMENT.destinationPath(
            orgName,
            recipeId,
            uploadedFile.filename
          );
        break;

      case "stepImages":
      case "stepMedia":
        if (!stepNumber) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: "stepNumber is required for step images",
          });
        }
        destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_INSTRUCTION_MEDIA.destinationPath(
            orgName,
            recipeId,
            stepNumber,
            uploadedFile.filename
          );
        break;

      case "recipeVideo":
        destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_VIDEO.destinationPath(
            orgName,
            recipeId,
            uploadedFile.filename
          );
        break;

      case "recipeAudio":
        destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_AUDIO.destinationPath(
            orgName,
            recipeId,
            uploadedFile.filename
          );
        break;

      case "recipeThumbnail":
        destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_THUMBNAIL.destinationPath(
            orgName,
            recipeId,
            uploadedFile.filename
          );
        break;

      case "nutritionLabel":
        destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_NUTRITION_LABELS.destinationPath(
            orgName,
            recipeId,
            uploadedFile.filename
          );
        break;

      default:
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: `Invalid uploadType: ${uploadType}. Supported types: recipePlaceholder, recipeFiles, stepImages, recipeVideo, recipeAudio, recipeThumbnail, nutritionLabel`,
        });
    }

    // Move file to the correct location if it's movable (not a duplicate)
    if (uploadedFile.isMovable) {
      // Track file operation for potential rollback
      fileTracker.trackMove(
        uploadedFile.path,
        destinationPath,
        uploadedFile.item_id
      );

      const moveResult = await uploadService.moveFileInBucket(
        bucketName,
        uploadedFile.path,
        destinationPath,
        uploadedFile.item_id
      );

      if (!moveResult.success) {
        // Rollback file operations on error
        await fileTracker.rollback();
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          status: false,
          message: `Failed to move file to destination: ${moveResult.error}`,
        });
      }
    }

    // Clear file tracker after successful operation
    fileTracker.clear();

    console.log(
      `Recipe file uploaded successfully: ${uploadedFile.originalname} -> ${destinationPath} (${uploadType})`
    );

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Recipe file uploaded successfully",
      data: {
        item_id: uploadedFile.item_id,
        original_name: uploadedFile.originalname,
        file_name: uploadedFile.filename,
        file_size: uploadedFile.size,
        mime_type: uploadedFile.mimetype,
        file_path: destinationPath,
        file_url: `${global.config.API_BASE_URL}/${destinationPath}`,
        item_type: uploadedFile.type,
        file_hash: uploadedFile.hash,
        upload_type: uploadType,
        is_duplicate: !uploadedFile.isMovable,
        recipe_id: recipeId,
        step_number: stepNumber,
      },
    });
  } catch (error: unknown) {
    // Rollback file operations on any error
    try {
      await fileTracker.rollback();
    } catch (rollbackError) {
      console.error("Error during file rollback:", rollbackError);
    }

    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error uploading recipe file"
    );
  }
};

// ============================================================================
// API 7: BULK DELETE UPLOADED FILES
// Handles: deletion of multiple uploaded files when user discards work
// ============================================================================

/**
 * @description Delete multiple uploaded files in bulk (for discard functionality)
 * @route DELETE /api/v1/recipes/batch/delete-files
 * @access Private
 * @functionality Deletes multiple uploaded files when user discards their work
 */
const bulkDeleteUploadedFiles = async (
  req: Request,
  res: Response
): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    const { recipe_resources, recipe_id } = req.body;
    const organizationId = req.user?.organization_id;

    // Find the file records that belong to the user's organization and are temporary
    const fileRecords = await Item.findAll({
      where: {
        id: {
          [Op.in]: recipe_resources,
        },
        item_organization_id: organizationId,
        item_status: item_status.DELETED, // Only allow deletion of temporary files
      },
      transaction,
    });

    // Update status to "delete" for all file records
    await Item.update(
      { item_status: "delete" },
      {
        where: {
          id: {
            [Op.in]: fileRecords.map((file: any) => file.id),
          },
        },
        transaction,
      }
    );
    // Commit transaction
    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("FILE_DELETED_SUCCESSFULLY"),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting multiple files"
    );
  }
};

// ============================================================================
// ICON SEEDER ENDPOINTS
// Handles: Uploading default icons for categories and attributes
// ============================================================================

/**
 * @description Upload icons for all recipe categories
 * @route POST /api/v1/public/upload/recipe-category-icons
 * @access Public
 */
const uploadRecipeCategoryIcons = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    console.log("📤 Starting recipe category icon upload...");

    // Get all recipe categories
    const categories = await db.sequelize.query(
      `SELECT category_name, category_icon FROM mo_category WHERE category_type = 'recipe' AND organization_id IS NULL AND is_system_category = true`,
      { type: QueryTypes.SELECT }
    );

    const iconMapping = iconSeederService.getRecipeCategoryIconMapping();
    const results = [];

    for (const category of categories as any[]) {
      const iconFileName = iconMapping[category.category_name];

      if (!iconFileName) {
        results.push({
          category: category.category_name,
          status: "skipped",
          reason: "No icon mapping found",
        });
        continue;
      }

      if (category.category_icon) {
        results.push({
          category: category.category_name,
          status: "skipped",
          reason: "Already has icon",
        });
        continue;
      }

      try {
        const iconId = await iconSeederService.uploadRecipeCategoryIcon(
          category.category_name,
          iconFileName
        );

        if (iconId) {
          // Update category with icon ID
          await db.sequelize.query(
            `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'recipe' AND organization_id IS NULL`,
            {
              replacements: { iconId, categoryName: category.category_name },
              type: QueryTypes.UPDATE,
            }
          );

          results.push({
            category: category.category_name,
            status: "success",
            iconId: iconId,
            fileName: iconFileName,
          });
        } else {
          results.push({
            category: category.category_name,
            status: "failed",
            reason: "Icon upload failed",
          });
        }
      } catch (error: any) {
        results.push({
          category: category.category_name,
          status: "error",
          reason: error.message,
        });
      }
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: "Recipe category icon upload completed",
      results: results,
      summary: {
        total: results.length,
        success: results.filter((r) => r.status === "success").length,
        skipped: results.filter((r) => r.status === "skipped").length,
        failed: results.filter(
          (r) => r.status === "failed" || r.status === "error"
        ).length,
      },
    });
  } catch (error: any) {
    console.error("Error uploading recipe category icons:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Failed to upload recipe category icons",
      error: error.message,
    });
  }
};

/**
 * @description Upload icons for all ingredient categories
 * @route POST /api/v1/public/upload/ingredient-category-icons
 * @access Public
 */
const uploadIngredientCategoryIcons = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    console.log("📤 Starting ingredient category icon upload...");

    // Get all ingredient categories
    const categories = await db.sequelize.query(
      `SELECT category_name, category_icon FROM mo_category WHERE category_type = 'ingredient' AND organization_id IS NULL AND is_system_category = true`,
      { type: QueryTypes.SELECT }
    );

    const iconMapping = iconSeederService.getIngredientCategoryIconMapping();
    const results = [];

    for (const category of categories as any[]) {
      const iconFileName = iconMapping[category.category_name];

      if (!iconFileName) {
        results.push({
          category: category.category_name,
          status: "skipped",
          reason: "No icon mapping found",
        });
        continue;
      }

      if (category.category_icon) {
        results.push({
          category: category.category_name,
          status: "skipped",
          reason: "Already has icon",
        });
        continue;
      }

      try {
        const iconId = await iconSeederService.uploadIngredientCategoryIcon(
          category.category_name,
          iconFileName
        );

        if (iconId) {
          // Update category with icon ID
          await db.sequelize.query(
            `UPDATE mo_category SET category_icon = :iconId WHERE category_name = :categoryName AND category_type = 'ingredient' AND organization_id IS NULL`,
            {
              replacements: { iconId, categoryName: category.category_name },
              type: QueryTypes.UPDATE,
            }
          );

          results.push({
            category: category.category_name,
            status: "success",
            iconId: iconId,
            fileName: iconFileName,
          });
        } else {
          results.push({
            category: category.category_name,
            status: "failed",
            reason: "Icon upload failed",
          });
        }
      } catch (error: any) {
        results.push({
          category: category.category_name,
          status: "error",
          reason: error.message,
        });
      }
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: "Ingredient category icon upload completed",
      results: results,
      summary: {
        total: results.length,
        success: results.filter((r) => r.status === "success").length,
        skipped: results.filter((r) => r.status === "skipped").length,
        failed: results.filter(
          (r) => r.status === "failed" || r.status === "error"
        ).length,
      },
    });
  } catch (error: any) {
    console.error("Error uploading ingredient category icons:", error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Failed to upload ingredient category icons",
      error: error.message,
    });
  }
};

export default {
  uploadSingleFile,
  uploadMultipleFiles,
  uploadRecipeFile,
  bulkDeleteUploadedFiles,
  uploadRecipeCategoryIcons,
  uploadIngredientCategoryIcons,
};
