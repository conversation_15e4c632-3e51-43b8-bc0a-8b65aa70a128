import { celebrate, Joi, Segments } from "celebrate";

// ============================================================================
// SIMPLIFIED ANALYTICS VALIDATORS - Only what's needed per feature document
// ============================================================================

// Valid CTA types from your requirements
const ctaTypes = [
  "contact_info", // Contact Info block
  "contact_form", // Contact Us form
  "custom_cta", // Custom CTA link
];

// Valid date range options for dashboard
const dateRangeOptions = [
  "today",
  "this_week",
  "this_month",
  "last_7_days",
  "last_month",
  "last_30_days",
  "last_90_days",
  "last_year",
  "custom", // For custom date range
];

// ============================================================================
// PUBLIC ANALYTICS VALIDATORS - Matching your feature requirements
// ============================================================================

/**
 * Validator for tracking recipe interactions on public recipes
 * Supports: recipe_view, recipe_bookmark, recipe_share
 * Essential for dashboard analytics requirements
 */
const trackRecipeViewValidator = () =>
  celebrate({
    [Segments.BODY]: {
      recipe_id: Joi.number().required(),
      recipe_name: Joi.string().optional(),
    },
  });

/**
 * Validator for tracking CTA clicks on public recipes
 * Tracks clicks on Contact Info, Contact Form, Custom CTA buttons
 */
const trackCtaClickValidator = () =>
  celebrate({
    [Segments.BODY]: {
      recipe_id: Joi.number().required(),
      recipe_name: Joi.string().optional(),
      cta_type: Joi.string()
        .valid(...ctaTypes)
        .required(),
      cta_text: Joi.string().optional(),
    },
  });

/**
 * Validator for contact form submissions from public recipes
 * Stores contact form data when users submit from public recipe pages
 */
const submitContactFormValidator = () =>
  celebrate({
    [Segments.BODY]: {
      recipe_id: Joi.number().required(),
      recipe_name: Joi.string().optional(),
      name: Joi.string().required(),
      email: Joi.string().email().required(),
      mobile: Joi.string().optional(),
      message: Joi.string().required(),
    },
  });

// ============================================================================
// DASHBOARD ANALYTICS VALIDATORS - For your 50/50 dashboard layout
// ============================================================================

/**
 * Validator for getting CTA click analytics with pagination (no default values)
 * Shows: Recipe Name, CTA Type, Clicks, Last Clicked At
 */
const getCtaClickAnalyticsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .optional(),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
      organization_id: Joi.string().optional(),
      sort_order: Joi.string().valid("asc", "desc").optional(),
      sort_by: Joi.string()
        .valid("clicked_at", "recipe_name", "cta_type")
        .optional(),
      search: Joi.string()
        .optional()
        .description("General search term for recipe name or CTA type"),
      recipe_name: Joi.string().optional(), // Search filter
      cta_type: Joi.string()
        .valid(...ctaTypes)
        .optional()
        .description("Filter by CTA type"),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    },
  });

/**
 * Validator for getting contact form submissions with pagination (no default values)
 * Shows: Recipe Name, Name, Email, Mobile, Message, Submitted On
 */
const getContactSubmissionsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .optional(),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
      search: Joi.string()
        .optional()
        .description(
          "General search term for recipe name, user name, or email"
        ),
      recipe_id: Joi.number().optional(), // Filter by recipe
      recipe_name: Joi.string()
        .optional()
        .description("Filter by recipe name (partial match)"),
      user_email: Joi.string()
        .email()
        .optional()
        .description("Filter by user email (partial match)"),
      sort_order: Joi.string().valid("asc", "desc").optional(),
      sort_by: Joi.string()
        .valid("submitted_at", "recipe_name", "contact_name")
        .optional(),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    },
  });

/**
 * Validator for exporting contact form submissions
 * Supports CSV and Excel export formats
 */
const exportContactSubmissionsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      format: Joi.string().valid("csv", "excel").default("excel").optional(),
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .optional(),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
      search: Joi.string()
        .optional()
        .description(
          "General search term for recipe name, user name, or email"
        ),
      recipe_name: Joi.string()
        .optional()
        .description("Filter by recipe name (partial match)"),
      user_email: Joi.string()
        .email()
        .optional()
        .description("Filter by user email (partial match)"),
    },
  });

/**
 * Validator for deleting contact form submissions
 * Admin can delete individual submissions
 */
const deleteContactSubmissionValidator = () =>
  celebrate({
    [Segments.PARAMS]: {
      id: Joi.number().required(),
    },
  });

/**
 * Validator for analytics summary endpoint
 * Shows paginated analytics events with filtering options
 */
const getAnalyticsSummaryValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      page: Joi.number().min(1).default(1),
      limit: Joi.number().min(1).max(50).default(20),
      event_type: Joi.string()
        .valid("recipe_view", "cta_click", "contact_form_submit")
        .optional(),
      entity_type: Joi.string()
        .valid("recipe", "category", "ingredient")
        .optional(),
      entity_id: Joi.number().optional(),
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .default("last_30_days")
        .optional(),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
    },
  });

/**
 * Validator for recipe view analytics endpoint
 * Shows recipe view statistics with date filtering
 */
const getRecipeViewAnalyticsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .default("last_30_days"),
      start_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().required(),
        otherwise: Joi.date().iso().optional(),
      }),
      end_date: Joi.when("date_range", {
        is: "custom",
        then: Joi.date().iso().min(Joi.ref("start_date")).required(),
        otherwise: Joi.date().iso().optional(),
      }),
      sort_order: Joi.string().valid("asc", "desc").default("desc"),
      sort_by: Joi.string()
        .valid("view_count", "last_viewed", "recipe_name")
        .optional(),
    },
  });

/**
 * Validator for getting recipe view statistics for private recipes
 * Validates recipe ID parameter
 */
const getRecipeViewStatisticsValidator = () =>
  celebrate({
    [Segments.PARAMS]: {
      recipeId: Joi.number().required().positive(),
    },
    [Segments.QUERY]: {
      organization_id: Joi.string().optional(),
    },
  });

/**
 * Validator for resetting recipe view statistics for private recipes
 * Validates recipe ID parameter and optional user_ids query parameter
 */
const resetRecipeViewStatisticsValidator = () =>
  celebrate({
    [Segments.PARAMS]: {
      recipeId: Joi.string().required().pattern(/^\d+$/).messages({
        "string.pattern.base": "Recipe ID must be a valid positive number",
        "any.required": "Recipe ID is required",
      }),
      userId: Joi.string().optional().pattern(/^\d+$/).messages({
        "string.pattern.base":
          "User ID must be a valid positive number when provided",
      }),
    },
    [Segments.QUERY]: {
      organization_id: Joi.string().optional(),
      user_ids: Joi.alternatives()
        .try(
          Joi.string()
            .valid("all")
            .messages({
              "any.only": "user_ids must be either comma-separated user IDs or the string 'all'",
            }),
          Joi.string()
            .pattern(/^\d+(,\d+)*$/)
            .messages({
              "string.pattern.base": "user_ids must be comma-separated positive numbers (e.g., '55,56,57') or 'all'",
            })
        )
        .optional()
        .messages({
          "alternatives.match": "user_ids must be either comma-separated user IDs (e.g., '55,56,57') or the string 'all'",
        }),
    },
  });

/**
 * Validator for bulk deleting contact form submissions from analytics
 * Supports both specific IDs and filtered bulk delete
 */
const bulkDeleteContactSubmissionsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        submission_ids: Joi.array()
          .items(Joi.number().integer().min(1))
          .optional(),
        delete_all: Joi.boolean().default(false),
        filters: Joi.object()
          .keys({
            search: Joi.string().max(100).optional(),
            recipe_name: Joi.string().max(100).optional(),
            user_email: Joi.string().email().optional(),
            start_date: Joi.date().iso().optional(),
            end_date: Joi.date().iso().min(Joi.ref("start_date")).optional(),
          })
          .optional(),
      })
      .custom((value, helpers) => {
        // Validate that either submission_ids is provided or delete_all is true
        if (
          !value.delete_all &&
          (!value.submission_ids || value.submission_ids.length === 0)
        ) {
          return helpers.error("any.required");
        }
        return value;
      })
      .messages({
        "any.required":
          "Either provide submission_ids array or set delete_all to true with filters",
      }),
  });

export default {
  // Public tracking validators
  trackRecipeViewValidator,
  trackCtaClickValidator,
  submitContactFormValidator,

  // Dashboard analytics validators
  getCtaClickAnalyticsValidator,
  getContactSubmissionsValidator,
  exportContactSubmissionsValidator,
  deleteContactSubmissionValidator,
  bulkDeleteContactSubmissionsValidator,

  // Recipe view statistics validators
  getRecipeViewStatisticsValidator,
  resetRecipeViewStatisticsValidator,
};
